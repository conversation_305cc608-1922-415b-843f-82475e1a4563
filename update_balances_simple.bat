@echo off
echo ===============================================
echo    Monarch Money Balance Update
echo ===============================================
echo.

REM Change to the script directory
cd /d "C:\Users\<USER>\web-scraper-to-excel"

echo Step 1: Navigating to Monarch Money accounts page...
python navigate_to_accounts.py
echo.

echo Step 2: Extracting account balances...
python run_scraper.py
echo.

echo Step 3: Checking what was written to Excel...
python check_excel_contents.py
echo.

echo ===============================================
echo Update process complete!
echo.
echo Next steps:
echo 1. Close this window
echo 2. Go back to Excel
echo 3. Press Ctrl+Shift+F9 to refresh
echo 4. Check cells E2-E8 for updated balances
echo ===============================================
echo.
pause
