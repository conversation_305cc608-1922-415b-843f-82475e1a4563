Sub UpdateMonarchBalances()
    '
    ' Update Monarch Money Balances - Ultimate Version
    ' Waits for completion flag file before finishing
    '
    
    Dim scriptPath As String
    Dim flagPath As String
    Dim startTime As Date
    Dim maxWaitTime As Integer
    Dim waitCount As Integer
    
    ' Paths
    scriptPath = "C:\Users\<USER>\web-scraper-to-excel\update_balances_invisible.vbs"
    flagPath = "C:\Users\<USER>\web-scraper-to-excel\update_complete.flag"
    
    ' Maximum wait time in seconds
    maxWaitTime = 45
    waitCount = 0
    
    ' Show status message
    Application.StatusBar = "Updating Monarch Money balances..."
    Application.ScreenUpdating = False
    
    ' Delete any existing flag file
    If Dir(flagPath) <> "" Then
        Kill flagPath
    End If
    
    ' Record start time
    startTime = Now
    
    ' Run the script invisibly
    Shell "wscript """ & scriptPath & """", vbHide
    
    ' Wait for completion flag file to appear
    Do While Dir(flagPath) = "" And DateDiff("s", startTime, Now) < maxWaitTime
        waitCount = waitCount + 1
        Application.StatusBar = "Updating Monarch Money balances... (" & waitCount & "s)"
        Application.Wait (Now + TimeValue("0:00:01"))
        DoEvents ' Allow Excel to process other events
    Loop
    
    ' Check if we completed successfully or timed out
    If Dir(flagPath) <> "" Then
        Application.StatusBar = "Processing results..."
        
        ' Wait an extra moment for file operations to complete
        Application.Wait (Now + TimeValue("0:00:02"))
        
        ' Force Excel to recalculate and refresh everything
        Application.Calculate
        Application.CalculateFullRebuild
        
        ' Specifically refresh the FIRE worksheet
        Dim ws As Worksheet
        Set ws = ThisWorkbook.Worksheets("FIRE")
        ws.Calculate
        
        ' Force Excel to re-read the file from disk
        ThisWorkbook.Save
        
        ' Force a complete screen refresh
        Application.ScreenUpdating = True
        Application.ScreenUpdating = False
        
        ' Clear status and re-enable screen updating
        Application.StatusBar = False
        Application.ScreenUpdating = True
        
        ' Show success message with details
        MsgBox "✅ Balance update completed successfully!" & vbCrLf & _
               "Updated at: " & Format(Now, "hh:mm:ss") & vbCrLf & _
               "Check cells E2-E8 for new balances." & vbCrLf & _
               "Timestamp in A1: " & ws.Range("A1").Value, _
               vbInformation, "Monarch Money Update"
        
        ' Clean up flag file
        If Dir(flagPath) <> "" Then
            Kill flagPath
        End If
        
    Else
        ' Timeout occurred
        Application.StatusBar = False
        Application.ScreenUpdating = True
        
        MsgBox "⚠️ Update may have timed out after " & maxWaitTime & " seconds." & vbCrLf & _
               "Please check your Chrome browser and try again." & vbCrLf & _
               "Make sure you're logged into Monarch Money.", _
               vbExclamation, "Update Timeout"
    End If
    
End Sub
