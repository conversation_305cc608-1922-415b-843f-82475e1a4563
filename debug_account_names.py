#!/usr/bin/env python3
"""
Debug script to see exactly what account names the scraper finds
vs. what's configured in the mapping
"""

import sys
import os
import json

# Add src directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from monarch_scraper import Monarch<PERSON><PERSON><PERSON><PERSON>rap<PERSON>

def debug_account_names():
    print("=== Account Name Debugging ===")
    print()
    
    # Load config to see what we're looking for
    with open('config.json', 'r') as f:
        config = json.load(f)
    
    target_accounts = list(config['cell_mapping'].keys())
    
    print("🎯 Target accounts from config:")
    for i, account in enumerate(target_accounts, 1):
        print(f"  {i}. '{account}'")
    print()
    
    # Connect to scraper and extract accounts
    scraper = MonarchMoneyScraper()
    
    if not scraper.connect():
        print("❌ Failed to connect to Chrome")
        return
    
    if not scraper.verify_monarch_page():
        print("❌ Not on Monarch Money page")
        return
    
    print("🔍 Extracting ALL account names found on page...")
    
    # Extract all accounts (not just targets) to see what's available
    all_accounts = scraper.extract_account_balances(target_accounts=None)
    
    print(f"\n📊 Found {len(all_accounts)} total accounts on page:")
    for i, (account_name, balance) in enumerate(all_accounts.items(), 1):
        # Highlight if this matches one of our targets
        is_target = account_name in target_accounts
        marker = "🎯" if is_target else "  "
        print(f"{marker} {i:2d}. '{account_name}' = {balance}")
    
    print("\n" + "="*60)
    print("🔍 MATCHING ANALYSIS:")
    print("="*60)
    
    # Check which targets were found and which weren't
    found_targets = []
    missing_targets = []
    
    for target in target_accounts:
        if target in all_accounts:
            found_targets.append(target)
            print(f"✅ FOUND: '{target}' = {all_accounts[target]}")
        else:
            missing_targets.append(target)
            print(f"❌ MISSING: '{target}'")
    
    if missing_targets:
        print(f"\n🔍 Looking for similar names for missing accounts...")
        for missing in missing_targets:
            print(f"\nMissing: '{missing}'")
            print("Similar names found on page:")
            
            # Look for partial matches
            missing_words = missing.lower().split()
            for account_name in all_accounts.keys():
                account_words = account_name.lower().split()
                
                # Check if any words match
                matches = [word for word in missing_words if any(word in account_word for account_word in account_words)]
                if matches:
                    print(f"  📝 '{account_name}' (matches: {matches})")
    
    print(f"\n📈 SUMMARY:")
    print(f"  ✅ Found: {len(found_targets)}/{len(target_accounts)} target accounts")
    print(f"  ❌ Missing: {len(missing_targets)} target accounts")
    
    scraper.close()

if __name__ == "__main__":
    debug_account_names()
