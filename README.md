# Monarch Money to Excel Scraper

This project scrapes account balance data from your Monarch Money account page when it's open in Chrome and automatically places the balances in specific cells within an Excel spreadsheet.

## Features

- 🔗 Connects to existing Chrome browser session (no need to handle login)
- 💰 Extracts account balances from Monarch Money pages
- 📊 Writes data to specific Excel cells with customizable mapping
- ⚙️ Configurable through JSON configuration file
- 🕒 Adds timestamps to track when data was last updated
- 🧪 Includes comprehensive testing suite

## Project Structure

```
web-scraper-to-excel/
├── src/
│   ├── scraper.py              # Main application orchestrator
│   ├── monarch_scraper.py      # Monarch Money specific scraping logic
│   ├── chrome_connector.py     # Chrome browser connection utilities
│   ├── excel_writer.py         # Excel file manipulation with cell mapping
│   └── utils/
│       └── __init__.py
├── config.json                 # Configuration file for Excel mapping
├── test_scraper.py            # Test suite
├── requirements.txt           # Project dependencies
└── README.md                  # This file
```

## Installation

1. **Clone the repository:**
   ```bash
   git clone <repository-url>
   cd web-scraper-to-excel
   ```

2. **Install Python dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

3. **Install ChromeDriver (if not already installed):**
   - The `webdriver-manager` package will automatically handle ChromeDriver installation
   - Alternatively, download from: https://chromedriver.chromium.org/

## Setup and Configuration

### 1. Configure Excel Mapping

Edit the `config.json` file to specify where account balances should be written:

```json
{
  "excel_file": "monarch_balances.xlsx",
  "sheet_name": "Account Balances",
  "chrome_debug_port": 9222,
  "cell_mapping": {
    "Checking Account": "B2",
    "Savings Account": "B3",
    "Credit Card": "B4",
    "Investment Account": "B5"
  },
  "auto_save": true,
  "add_timestamp": true,
  "timestamp_cell": "A1"
}
```

**Configuration Options:**
- `excel_file`: Path to your Excel file
- `sheet_name`: Name of the worksheet to use
- `cell_mapping`: Maps account names to specific Excel cells
- `auto_save`: Automatically save the Excel file after updating
- `add_timestamp`: Add a timestamp showing when data was last updated
- `timestamp_cell`: Cell where timestamp should be placed

### 2. Start Chrome with Remote Debugging

Before running the scraper, you need to start Chrome with remote debugging enabled:

**Windows:**
```cmd
"C:\Program Files\Google\Chrome\Application\chrome.exe" --remote-debugging-port=9222 --user-data-dir="C:\temp\chrome_debug"
```

**Mac:**
```bash
/Applications/Google\ Chrome.app/Contents/MacOS/Google\ Chrome --remote-debugging-port=9222 --user-data-dir="/tmp/chrome_debug"
```

**Linux:**
```bash
google-chrome --remote-debugging-port=9222 --user-data-dir="/tmp/chrome_debug"
```

### 3. Navigate to Monarch Money

1. In the Chrome window that opened, navigate to your Monarch Money account
2. Log in and navigate to the page with your account balances
3. Leave this Chrome window open

## Usage

### Basic Usage

Run the scraper:
```bash
python src/scraper.py
```

The scraper will:
1. Connect to your open Chrome session
2. Verify you're on a Monarch Money page
3. Extract account balance data
4. Write the data to your Excel file according to your configuration
5. Save the Excel file with a timestamp

### Testing

Run the test suite to verify everything is working:
```bash
python test_scraper.py
```

This will run:
- Unit tests for individual components
- Integration tests for the complete workflow
- Optional Chrome connection test

## Troubleshooting

### Common Issues

1. **"Failed to connect to Chrome"**
   - Make sure Chrome is running with the `--remote-debugging-port=9222` flag
   - Check that no other applications are using port 9222
   - Try restarting Chrome with the debug flag

2. **"No account data found"**
   - Verify you're on the correct Monarch Money page with account balances visible
   - The page may need time to load - try waiting a few seconds and running again
   - Check that account balance elements are visible on the page

3. **"Failed to write to Excel"**
   - Make sure the Excel file isn't open in another application
   - Check that you have write permissions to the file location
   - Verify the cell addresses in your configuration are valid (e.g., "A1", "B2")

4. **Selenium/ChromeDriver Issues**
   - Update Chrome to the latest version
   - The `webdriver-manager` should automatically handle ChromeDriver compatibility
   - If issues persist, try manually installing ChromeDriver

### Debug Mode

For more detailed output, you can modify the scraper to include debug information:

```python
# In src/scraper.py, add more print statements or logging
```

## Customization

### Adding New Account Types

To handle additional account types or different page layouts:

1. Modify the selectors in `src/monarch_scraper.py`
2. Update the `extract_account_balances()` method
3. Test with your specific Monarch Money page layout

### Custom Excel Formatting

You can add custom formatting to Excel cells by modifying the `ExcelWriter` class:

```python
# Example: Add bold formatting to account names
format_options = {
    'font': {'bold': True},
    'alignment': {'horizontal': 'center'}
}
writer.write_to_cell('A1', 'Account Name', format_options)
```

## Security Considerations

- This tool connects to your existing Chrome session, so it uses your existing login
- No credentials are stored or transmitted by this application
- The Chrome debug port is only accessible locally
- Always keep your Chrome browser and this tool updated

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License

This project is licensed under the MIT License.