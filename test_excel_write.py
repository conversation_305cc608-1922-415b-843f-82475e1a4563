#!/usr/bin/env python3
"""
Simple test script to verify Excel writing works
"""

import sys
import os
import json
from datetime import datetime

# Add src directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from excel_writer import ExcelWriter

def test_excel_write():
    print("=== Excel Write Test ===")
    
    # Load config
    with open('config.json', 'r') as f:
        config = json.load(f)
    
    excel_file = config['excel_file']
    sheet_name = config['sheet_name']
    
    print(f"Testing write to: {excel_file}")
    print(f"Sheet: {sheet_name}")
    
    try:
        # Create Excel writer
        writer = ExcelWriter(excel_file)
        
        # Load workbook
        if writer.load_or_create_workbook(sheet_name):
            print("✅ Workbook loaded successfully")
            
            # Write test data
            test_timestamp = f"TEST: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            
            if writer.write_to_cell("A1", test_timestamp):
                print(f"✅ Wrote timestamp to A1: {test_timestamp}")
            
            # Write test values to your balance cells
            test_data = {
                "Kate's Roth IRA": "TEST: $100,000.00",
                "Kate Self Employed 401K": "TEST: $200,000.00", 
                "Dan's Roth IRA": "TEST: $300,000.00",
                "EY 401K": "TEST: $400,000.00",
                "Dan Self Employed 401K": "TEST: $500,000.00",
                "Health Savings Account": "TEST: $600,000.00",
                "General Investments": "TEST: $700,000.00"
            }
            
            cell_mapping = config['cell_mapping']
            
            for account, test_value in test_data.items():
                if account in cell_mapping:
                    cell = cell_mapping[account]
                    if writer.write_to_cell(cell, test_value):
                        print(f"✅ Wrote {account} to {cell}: {test_value}")
                    else:
                        print(f"❌ Failed to write {account} to {cell}")
            
            # Save the workbook
            if writer.save_workbook():
                print("✅ Workbook saved successfully")
                print()
                print("🎯 CHECK YOUR EXCEL FILE NOW!")
                print("You should see:")
                print("  - A1: TEST timestamp")
                print("  - E2-E8: TEST values")
                print()
                print("If you see these values, the Excel writing works!")
                print("If not, there's a file path or permission issue.")
            else:
                print("❌ Failed to save workbook")
            
            writer.close()
            
        else:
            print("❌ Failed to load workbook")
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")

if __name__ == "__main__":
    test_excel_write()
