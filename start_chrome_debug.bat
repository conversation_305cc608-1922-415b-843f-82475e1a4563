@echo off
echo Starting Chrome with remote debugging enabled...
echo.
echo This will:
echo 1. Close any existing Chrome windows
echo 2. Start Chrome with remote debugging on port 9222
echo 3. Open a new Chrome window for you to navigate to Monarch Money
echo.
echo Press any key to continue or Ctrl+C to cancel...
pause >nul

echo.
echo Closing existing Chrome processes...
taskkill /f /im chrome.exe >nul 2>&1

echo Starting Chrome with debugging enabled...
echo.
echo Navigate to your Monarch Money account in the Chrome window that opens.
echo Then run: python src/scraper.py
echo.

start "" "C:\Program Files\Google\Chrome\Application\chrome.exe" --remote-debugging-port=9222 --user-data-dir="%TEMP%\chrome_debug"

echo Chrome started with remote debugging enabled.
echo You can now run the scraper!
pause
