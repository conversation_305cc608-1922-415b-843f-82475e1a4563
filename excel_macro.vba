Sub UpdateMonarchBalances()
    '
    ' UpdateMonarchBalances Macro
    ' Updates account balances from Monarch Money
    '
    
    Dim scriptPath As String
    Dim result As Integer
    
    ' Path to your Python script
    scriptPath = "C:\Users\<USER>\web-scraper-to-excel\run_scraper.py"
    
    ' Show a message that update is starting
    Application.StatusBar = "Updating Monarch Money balances..."
    Application.ScreenUpdating = False
    
    ' Run the Python script
    result = Shell("python """ & scriptPath & """", vbNormalFocus)
    
    ' Wait a moment for the script to complete
    Application.Wait (Now + TimeValue("0:00:05"))
    
    ' Refresh the current workbook to show updated values
    ThisWorkbook.RefreshAll
    
    ' Reset status
    Application.StatusBar = False
    Application.ScreenUpdating = True
    
    ' Show completion message
    MsgBox "Balance update complete! Check cells E2-E8 for updated values.", vbInformation, "Monarch Money Update"
    
End Sub
