@echo off
echo ===============================================
echo    Monarch Money Balance Update + Auto Refresh
echo ===============================================
echo.

REM Change to the script directory
cd /d "C:\Users\<USER>\web-scraper-to-excel"

echo Step 1: Navigating to Monarch Money accounts page...
python navigate_to_accounts.py
echo.

echo Step 2: Extracting account balances...
python run_scraper.py
echo.

echo Step 3: Checking what was written to Excel...
python check_excel_contents.py
echo.

echo Step 4: Closing Excel to refresh data...
taskkill /f /im excel.exe >nul 2>&1
timeout /t 2 >nul

echo Step 5: Reopening Excel with updated data...
start "" "C:\Users\<USER>\OneDrive\Financial Plan\Financial_Plan.xlsx"

echo.
echo ===============================================
echo ✅ Update complete! 
echo Excel has been reopened with fresh data.
echo Check cells E2-E8 for updated balances!
echo ===============================================
echo.
pause
