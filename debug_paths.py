#!/usr/bin/env python3
"""
Debug script to check file paths and permissions
"""

import os
import json
from datetime import datetime

def check_paths():
    print("=== Path Debugging ===")
    print(f"Current working directory: {os.getcwd()}")
    print()
    
    # Check config file
    config_path = "config.json"
    if os.path.exists(config_path):
        print(f"✅ Config file found: {config_path}")
        with open(config_path, 'r') as f:
            config = json.load(f)
        excel_file = config.get('excel_file')
        print(f"Excel file path in config: {excel_file}")
        
        # Check if Excel file exists
        if os.path.exists(excel_file):
            print(f"✅ Excel file found: {excel_file}")
            
            # Check file permissions
            if os.access(excel_file, os.R_OK):
                print("✅ Excel file is readable")
            else:
                print("❌ Excel file is NOT readable")
                
            if os.access(excel_file, os.W_OK):
                print("✅ Excel file is writable")
            else:
                print("❌ Excel file is NOT writable")
                
            # Check file modification time
            mod_time = os.path.getmtime(excel_file)
            mod_datetime = datetime.fromtimestamp(mod_time)
            print(f"Excel file last modified: {mod_datetime}")
            
        else:
            print(f"❌ Excel file NOT found: {excel_file}")
    else:
        print(f"❌ Config file NOT found: {config_path}")
    
    print()
    print("=== File Listing ===")
    files = os.listdir(".")
    for file in sorted(files):
        if file.endswith(('.py', '.json', '.xlsx', '.vbs', '.bat')):
            print(f"  {file}")

if __name__ == "__main__":
    check_paths()
