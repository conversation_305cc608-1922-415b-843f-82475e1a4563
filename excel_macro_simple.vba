Sub UpdateMonarchBalances()
    '
    ' Update Monarch Money Balances - Simple Version
    ' Saves file, runs update, asks user to refresh
    '
    
    Dim scriptPath As String
    Dim response As VbMsgBoxResult
    
    ' Path to your invisible VBS script
    scriptPath = "C:\Users\<USER>\web-scraper-to-excel\update_balances_invisible.vbs"
    
    ' Save the current file first
    ThisWorkbook.Save
    
    ' Show status message
    Application.StatusBar = "Running Monarch Money update..."
    
    ' Run the script invisibly
    Shell "wscript """ & scriptPath & """", vbHide
    
    ' Show message asking user to wait and then refresh
    response = MsgBox("Monarch Money update is running in the background." & vbCrLf & _
                     "This will take about 10-15 seconds." & vbCrLf & vbCrLf & _
                     "Click OK when you're ready to refresh the data.", _
                     vbOKOnly + vbInformation, "Update Running")
    
    ' When user clicks OK, refresh the file
    Application.StatusBar = "Refreshing data..."
    
    ' Close and reopen the file to get fresh data
    Dim currentPath As String
    currentPath = ThisWorkbook.FullName
    
    Application.DisplayAlerts = False
    ThisWorkbook.Close SaveChanges:=False
    Application.DisplayAlerts = True
    
    ' Reopen the file
    Dim wb As Workbook
    Set wb = Application.Workbooks.Open(currentPath)
    
    ' Clear status
    Application.StatusBar = False
    
    ' Show completion message
    Dim ws As Worksheet
    Set ws = wb.Worksheets("FIRE")
    
    MsgBox "✅ File refreshed!" & vbCrLf & _
           "Check cells E2-E8 for updated balances." & vbCrLf & _
           "Timestamp in A1: " & ws.Range("A1").Value, _
           vbInformation, "Update Complete"
    
End Sub
