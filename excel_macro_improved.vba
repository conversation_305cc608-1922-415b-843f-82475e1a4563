Sub UpdateMonarchBalances()
    '
    ' Update Monarch Money Balances - Improved Version
    '
    
    Dim scriptPath As String
    Dim startTime As Date
    Dim maxWaitTime As Integer
    
    ' Path to your invisible VBS script
    scriptPath = "C:\Users\<USER>\web-scraper-to-excel\update_balances_invisible.vbs"
    
    ' Maximum wait time in seconds
    maxWaitTime = 30
    
    ' Show status message
    Application.StatusBar = "Updating Monarch Money balances..."
    Application.ScreenUpdating = False
    
    ' Record start time
    startTime = Now
    
    ' Run the script invisibly
    Shell "wscript """ & scriptPath & """", vbHide
    
    ' Wait for the script to complete with better timing
    Do While DateDiff("s", startTime, Now) < maxWaitTime
        Application.Wait (Now + TimeValue("0:00:01"))
        DoEvents ' Allow Excel to process other events
    Loop
    
    ' Force Excel to recalculate and refresh
    Application.Calculate
    Application.CalculateFullRebuild
    
    ' Specifically refresh the FIRE worksheet
    Dim ws As Worksheet
    Set ws = ThisWorkbook.Worksheets("FIRE")
    ws.Calculate
    
    ' Force a screen update
    Application.ScreenUpdating = True
    Application.ScreenUpdating = False
    
    ' Clear status and re-enable screen updating
    Application.StatusBar = False
    Application.ScreenUpdating = True
    
    ' Show completion message with current timestamp
    MsgBox "Balance update completed at " & Format(Now, "hh:mm:ss") & vbCrLf & _
           "Check cells E2-E8 for updated values." & vbCrLf & _
           "Timestamp in A1: " & ws.Range("A1").Value, _
           vbInformation, "Monarch Money Update"
    
End Sub
