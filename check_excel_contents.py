#!/usr/bin/env python3
"""
Script to check what's actually in the Excel file
"""

import sys
import os
import json
from datetime import datetime

# Add src directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from openpyxl import load_workbook

def check_excel_contents():
    print("=== Excel File Contents Check ===")
    
    # Load config
    with open('config.json', 'r') as f:
        config = json.load(f)
    
    excel_file = config['excel_file']
    sheet_name = config['sheet_name']
    cell_mapping = config['cell_mapping']
    
    print(f"Checking file: {excel_file}")
    print(f"Sheet: {sheet_name}")
    print()
    
    if not os.path.exists(excel_file):
        print(f"❌ Excel file not found: {excel_file}")
        return
    
    # Check file modification time
    mod_time = os.path.getmtime(excel_file)
    mod_datetime = datetime.fromtimestamp(mod_time)
    print(f"File last modified: {mod_datetime}")
    print()
    
    try:
        # Load the workbook
        workbook = load_workbook(excel_file)
        
        if sheet_name not in workbook.sheetnames:
            print(f"❌ Sheet '{sheet_name}' not found in workbook")
            print(f"Available sheets: {workbook.sheetnames}")
            return
        
        worksheet = workbook[sheet_name]
        
        # Check timestamp cell
        timestamp_cell = config.get('timestamp_cell', 'A1')
        timestamp_value = worksheet[timestamp_cell].value
        print(f"Timestamp in {timestamp_cell}: {timestamp_value}")
        print()
        
        # Check all mapped cells
        print("Account balance cells:")
        for account_name, cell_address in cell_mapping.items():
            cell_value = worksheet[cell_address].value
            print(f"  {cell_address} ({account_name}): {cell_value}")
        
        print()
        
        # Check if any cells have values
        has_values = any(worksheet[cell].value for cell in cell_mapping.values())
        if has_values:
            print("✅ Some cells have values")
        else:
            print("❌ All balance cells are empty")
        
        workbook.close()
        
    except Exception as e:
        print(f"❌ Error reading Excel file: {str(e)}")

if __name__ == "__main__":
    check_excel_contents()
