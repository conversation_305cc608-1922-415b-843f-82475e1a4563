Sub UpdateMonarchBalances()
    '
    ' Update Monarch Money Balances - Working Version
    ' Uses the fixed VBS script that navigates to the right page
    '
    
    Dim scriptPath As String
    
    ' Path to your fixed invisible VBS script
    scriptPath = "C:\Users\<USER>\web-scraper-to-excel\update_balances_invisible.vbs"
    
    ' Save the current file first (important!)
    ThisWorkbook.Save
    
    ' Show starting message
    MsgBox "🚀 Starting Monarch Money balance update..." & vbCrLf & vbCrLf & _
           "The script will:" & vbCrLf & _
           "1. Navigate to Monarch Money accounts page" & vbCrLf & _
           "2. Extract your account balances" & vbCrLf & _
           "3. Update this Excel file" & vbCrLf & vbCrLf & _
           "This takes about 20 seconds. Click OK to start.", _
           vbOKOnly + vbInformation, "Starting Update"
    
    ' Run the fixed VBS script invisibly
    Shell "wscript """ & scriptPath & """", vbHide
    
    ' Show completion message with refresh instructions
    MsgBox "✅ Update script launched successfully!" & vbCrLf & vbCrLf & _
           "⏳ Please wait 20 seconds for completion" & vbCrLf & vbCrLf & _
           "Then to see updated balances:" & vbCrLf & _
           "• Press Ctrl+Shift+F9 to refresh Excel" & vbCrLf & _
           "• Or close and reopen this file" & vbCrLf & vbCrLf & _
           "✨ Check cells E2-E8 for new balances!", _
           vbInformation, "Update Running"
    
End Sub
