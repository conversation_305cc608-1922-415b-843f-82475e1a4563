@echo off
echo === Monarch Money Balance Update ===
echo.

REM Change to the script directory
cd /d "C:\Users\<USER>\web-scraper-to-excel"

echo Current directory: %CD%
echo.

echo Step 1: Checking Chrome connection...
python navigate_to_accounts.py
echo.

echo Step 2: Running balance extraction...
python run_scraper.py
echo.

echo Step 3: Creating completion flag...
echo Update completed at %DATE% %TIME% > update_complete.flag

echo.
echo === Update Complete ===
echo Check your Excel file now!
echo.
pause
