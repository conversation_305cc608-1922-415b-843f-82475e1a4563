Sub UpdateMonarchBalances()
    '
    ' Update Monarch Money Balances - Reliable Version
    ' Simple approach that works every time
    '
    
    Dim scriptPath As String
    
    ' Path to your invisible VBS script
    scriptPath = "C:\Users\<USER>\web-scraper-to-excel\update_balances_invisible.vbs"
    
    ' Save the current file first (important!)
    ThisWorkbook.Save
    
    ' Show initial message
    MsgBox "Starting Monarch Money balance update..." & vbCrLf & _
           "This will take about 10-15 seconds." & vbCrLf & vbCrLf & _
           "Click OK to start the update.", _
           vbOKOnly + vbInformation, "Starting Update"
    
    ' Run the script invisibly
    Shell "wscript """ & scriptPath & """", vbHide
    
    ' Show completion message with instructions
    MsgBox "✅ Update script has been launched!" & vbCrLf & vbCrLf & _
           "To see the updated balances:" & vbCrLf & _
           "1. Wait 15 seconds for the update to complete" & vbCrLf & _
           "2. Press Ctrl+Shift+F9 to refresh Excel" & vbCrLf & _
           "3. Or close and reopen this file" & vbCrLf & vbCrLf & _
           "Check cells E2-E8 for new balances!", _
           vbInformation, "Update Launched"
    
End Sub
