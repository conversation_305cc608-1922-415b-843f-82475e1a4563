"""
Chrome Connector Module

This module provides utilities to connect to an existing Chrome browser session
and interact with web pages for scraping purposes.
"""

import time
import json
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException


class ChromeConnector:
    """
    A class to connect to an existing Chrome browser session and perform web scraping operations.
    """
    
    def __init__(self, debug_port=9222):
        """
        Initialize the Chrome connector.
        
        Args:
            debug_port (int): The debug port Chrome should be running on (default: 9222)
        """
        self.debug_port = debug_port
        self.driver = None
        
    def connect_to_existing_chrome(self):
        """
        Connect to an existing Chrome session running with remote debugging enabled.
        
        Returns:
            bool: True if connection successful, False otherwise
        """
        try:
            chrome_options = Options()
            chrome_options.add_experimental_option("debuggerAddress", f"localhost:{self.debug_port}")
            
            # Create the driver instance
            self.driver = webdriver.Chrome(options=chrome_options)
            
            print(f"Successfully connected to Chrome on port {self.debug_port}")
            return True
            
        except Exception as e:
            print(f"Failed to connect to Chrome: {str(e)}")
            print("Make sure Chrome is running with --remote-debugging-port=9222")
            return False
    
    def get_current_url(self):
        """
        Get the current URL of the active tab.
        
        Returns:
            str: Current URL or None if not connected
        """
        if not self.driver:
            print("Not connected to Chrome")
            return None
        
        try:
            return self.driver.current_url
        except Exception as e:
            print(f"Error getting current URL: {str(e)}")
            return None
    
    def wait_for_element(self, selector, by=By.CSS_SELECTOR, timeout=10):
        """
        Wait for an element to be present on the page.
        
        Args:
            selector (str): The selector to find the element
            by (By): The method to locate the element (default: CSS_SELECTOR)
            timeout (int): Maximum time to wait in seconds
            
        Returns:
            WebElement: The found element or None if not found
        """
        if not self.driver:
            print("Not connected to Chrome")
            return None
        
        try:
            element = WebDriverWait(self.driver, timeout).until(
                EC.presence_of_element_located((by, selector))
            )
            return element
        except TimeoutException:
            print(f"Element with selector '{selector}' not found within {timeout} seconds")
            return None
    
    def find_elements(self, selector, by=By.CSS_SELECTOR):
        """
        Find multiple elements on the page.
        
        Args:
            selector (str): The selector to find elements
            by (By): The method to locate elements (default: CSS_SELECTOR)
            
        Returns:
            list: List of WebElements or empty list if none found
        """
        if not self.driver:
            print("Not connected to Chrome")
            return []
        
        try:
            return self.driver.find_elements(by, selector)
        except Exception as e:
            print(f"Error finding elements: {str(e)}")
            return []
    
    def get_element_text(self, element):
        """
        Get text content from a WebElement.
        
        Args:
            element: WebElement to get text from
            
        Returns:
            str: Text content or empty string if error
        """
        try:
            return element.text.strip()
        except Exception as e:
            print(f"Error getting element text: {str(e)}")
            return ""
    
    def execute_script(self, script):
        """
        Execute JavaScript in the browser.
        
        Args:
            script (str): JavaScript code to execute
            
        Returns:
            Any: Result of the script execution
        """
        if not self.driver:
            print("Not connected to Chrome")
            return None
        
        try:
            return self.driver.execute_script(script)
        except Exception as e:
            print(f"Error executing script: {str(e)}")
            return None
    
    def close_connection(self):
        """
        Close the connection to Chrome (but don't close the browser).
        """
        if self.driver:
            try:
                self.driver.quit()
                print("Chrome connection closed")
            except Exception as e:
                print(f"Error closing Chrome connection: {str(e)}")
            finally:
                self.driver = None


def start_chrome_with_debugging():
    """
    Instructions for starting Chrome with remote debugging enabled.
    This function prints instructions since we can't automatically start Chrome.
    """
    instructions = """
    To use this scraper, you need to start Chrome with remote debugging enabled.
    
    Steps:
    1. Close all Chrome windows
    2. Open Command Prompt or Terminal
    3. Run one of these commands:
    
    Windows:
    "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe" --remote-debugging-port=9222 --user-data-dir="C:\\temp\\chrome_debug"
    
    Mac:
    /Applications/Google\\ Chrome.app/Contents/MacOS/Google\\ Chrome --remote-debugging-port=9222 --user-data-dir="/tmp/chrome_debug"
    
    Linux:
    google-chrome --remote-debugging-port=9222 --user-data-dir="/tmp/chrome_debug"
    
    4. Navigate to your Monarch Money account in the opened Chrome window
    5. Run this scraper program
    """
    print(instructions)
    return instructions
