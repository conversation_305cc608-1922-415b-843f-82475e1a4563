Sub UpdateMonarchBalances()
    '
    ' Update Monarch Money Balances - Final Version
    ' Closes file, runs update, then reopens file
    '
    
    Dim scriptPath As String
    Dim flagPath As String
    Dim currentFilePath As String
    Dim startTime As Date
    Dim maxWaitTime As Integer
    Dim waitCount As Integer
    
    ' Paths
    scriptPath = "C:\Users\<USER>\web-scraper-to-excel\update_balances_invisible.vbs"
    flagPath = "C:\Users\<USER>\web-scraper-to-excel\update_complete.flag"
    currentFilePath = ThisWorkbook.FullName
    
    ' Maximum wait time in seconds
    maxWaitTime = 45
    waitCount = 0
    
    ' Show status message
    Application.StatusBar = "Preparing to update Monarch Money balances..."
    Application.ScreenUpdating = False
    Application.DisplayAlerts = False
    
    ' Delete any existing flag file
    If Dir(flagPath) <> "" Then
        Kill flagPath
    End If
    
    ' Save the current file first
    ThisWorkbook.Save
    
    ' Close this workbook to allow Python to write to it
    Application.StatusBar = "Closing file for update..."
    ThisWorkbook.Close SaveChanges:=True
    
    ' Record start time
    startTime = Now
    
    ' Run the script invisibly
    Shell "wscript """ & scriptPath & """", vbHide
    
    ' Wait for completion flag file to appear
    Do While Dir(flagPath) = "" And DateDiff("s", startTime, Now) < maxWaitTime
        waitCount = waitCount + 1
        Application.StatusBar = "Updating Monarch Money balances... (" & waitCount & "s)"
        Application.Wait (Now + TimeValue("0:00:01"))
        DoEvents ' Allow Excel to process other events
    Loop
    
    ' Reopen the workbook
    Application.StatusBar = "Reopening updated file..."
    Dim wb As Workbook
    Set wb = Application.Workbooks.Open(currentFilePath)
    
    ' Re-enable everything
    Application.DisplayAlerts = True
    Application.ScreenUpdating = True
    Application.StatusBar = False
    
    ' Check if we completed successfully or timed out
    If Dir(flagPath) <> "" Then
        ' Success - show completion message
        Dim ws As Worksheet
        Set ws = wb.Worksheets("FIRE")
        
        MsgBox "✅ Balance update completed successfully!" & vbCrLf & _
               "Updated at: " & Format(Now, "hh:mm:ss") & vbCrLf & _
               "Check cells E2-E8 for new balances." & vbCrLf & _
               "Timestamp in A1: " & ws.Range("A1").Value, _
               vbInformation, "Monarch Money Update"
        
        ' Clean up flag file
        If Dir(flagPath) <> "" Then
            Kill flagPath
        End If
        
    Else
        ' Timeout occurred
        MsgBox "⚠️ Update may have timed out after " & maxWaitTime & " seconds." & vbCrLf & _
               "Please check your Chrome browser and try again." & vbCrLf & _
               "Make sure you're logged into Monarch Money.", _
               vbExclamation, "Update Timeout"
    End If
    
End Sub
