"""
Excel Writer Module

Enhanced module for writing data to specific cells in Excel spreadsheets,
with support for existing files and flexible cell mapping.
"""

import os
from openpyxl import Workbook, load_workbook
from openpyxl.utils import get_column_letter
from openpyxl.styles import <PERSON>ont, Alignment, PatternFill


class ExcelWriter:
    """
    A class for writing data to Excel files with support for specific cell placement.
    """

    def __init__(self, file_path='output.xlsx', password=None):
        """
        Initialize the Excel writer.

        Args:
            file_path (str): Path to the Excel file
            password (str): Password for protected Excel file (optional)
        """
        self.file_path = file_path
        self.password = password
        self.workbook = None
        self.worksheet = None

    def load_or_create_workbook(self, sheet_name=None):
        """
        Load an existing workbook or create a new one.

        Args:
            sheet_name (str): Name of the worksheet to use (optional)

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            if os.path.exists(self.file_path):
                print(f"Loading existing workbook: {self.file_path}")

                # Try to load with password if provided
                if self.password:
                    try:
                        # For password-protected files, we need to use msoffcrypto-tool
                        import msoffcrypto
                        import io

                        # Read the encrypted file
                        with open(self.file_path, 'rb') as f:
                            office_file = msoffcrypto.OfficeFile(f)
                            office_file.load_key(password=self.password)

                            # Decrypt to memory
                            decrypted = io.BytesIO()
                            office_file.save(decrypted)
                            decrypted.seek(0)

                            # Load the decrypted workbook
                            self.workbook = load_workbook(decrypted)
                            print("Successfully opened password-protected file")

                    except ImportError:
                        print("msoffcrypto-tool not installed. Installing it now...")
                        import subprocess
                        import sys
                        subprocess.check_call([sys.executable, "-m", "pip", "install", "msoffcrypto-tool"])

                        # Try again after installation
                        import msoffcrypto
                        import io

                        with open(self.file_path, 'rb') as f:
                            office_file = msoffcrypto.OfficeFile(f)
                            office_file.load_key(password=self.password)

                            decrypted = io.BytesIO()
                            office_file.save(decrypted)
                            decrypted.seek(0)

                            self.workbook = load_workbook(decrypted)
                            print("Successfully opened password-protected file")

                    except Exception as pwd_error:
                        print(f"Error opening password-protected file: {str(pwd_error)}")
                        print("Please check that the password is correct")
                        return False
                else:
                    # Try to load without password first
                    try:
                        self.workbook = load_workbook(self.file_path)
                    except Exception as e:
                        if "password" in str(e).lower() or "encrypted" in str(e).lower():
                            print("File appears to be password-protected but no password provided")
                            print("Please add the password to your configuration")
                            return False
                        else:
                            raise e
            else:
                print(f"Creating new workbook: {self.file_path}")
                self.workbook = Workbook()

            # Select or create worksheet
            if sheet_name:
                if sheet_name in self.workbook.sheetnames:
                    self.worksheet = self.workbook[sheet_name]
                else:
                    self.worksheet = self.workbook.create_sheet(sheet_name)
            else:
                self.worksheet = self.workbook.active

            return True

        except Exception as e:
            print(f"Error loading/creating workbook: {str(e)}")
            return False

    def write_to_cell(self, cell_address, value, format_options=None):
        """
        Write a value to a specific cell.

        Args:
            cell_address (str): Cell address (e.g., 'A1', 'B5')
            value: Value to write to the cell
            format_options (dict): Optional formatting options

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            if not self.worksheet:
                print("No worksheet loaded. Call load_or_create_workbook() first.")
                return False

            # Write the value
            self.worksheet[cell_address] = value

            # Apply formatting if provided
            if format_options:
                self._apply_cell_formatting(cell_address, format_options)

            print(f"Written '{value}' to cell {cell_address}")
            return True

        except Exception as e:
            print(f"Error writing to cell {cell_address}: {str(e)}")
            return False

    def write_account_balances(self, account_data, cell_mapping=None):
        """
        Write account balance data to specific cells.

        Args:
            account_data (dict): Dictionary of account names and balances
            cell_mapping (dict): Mapping of account names to cell addresses

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            if not self.worksheet:
                print("No worksheet loaded. Call load_or_create_workbook() first.")
                return False

            success_count = 0

            for account_name, balance in account_data.items():
                # Determine cell address
                if cell_mapping and account_name in cell_mapping:
                    cell_address = cell_mapping[account_name]
                else:
                    # If no mapping provided, write sequentially starting from A1
                    row = len([k for k in account_data.keys()
                             if list(account_data.keys()).index(k) <=
                             list(account_data.keys()).index(account_name)]) + 1
                    cell_address = f"A{row}"

                # Clean the balance value (remove currency symbols, etc.)
                clean_balance = self._clean_currency_value(balance)

                # Write to cell
                if self.write_to_cell(cell_address, clean_balance):
                    success_count += 1

                # Also write account name to adjacent cell if no specific mapping
                if not cell_mapping:
                    name_cell = f"B{row}"
                    self.write_to_cell(name_cell, account_name)

            print(f"Successfully wrote {success_count}/{len(account_data)} account balances")
            return success_count > 0

        except Exception as e:
            print(f"Error writing account balances: {str(e)}")
            return False

    def _clean_currency_value(self, value):
        """
        Clean currency value for Excel (remove symbols, convert to number).

        Args:
            value (str): Currency value string

        Returns:
            float or str: Cleaned numeric value or original string if conversion fails
        """
        try:
            if isinstance(value, (int, float)):
                return value

            # Remove common currency symbols and formatting
            clean_value = str(value).replace('$', '').replace(',', '').replace(' USD', '').strip()

            # Try to convert to float
            return float(clean_value)

        except (ValueError, TypeError):
            # If conversion fails, return original value
            return value

    def _apply_cell_formatting(self, cell_address, format_options):
        """
        Apply formatting to a cell.

        Args:
            cell_address (str): Cell address
            format_options (dict): Formatting options
        """
        try:
            cell = self.worksheet[cell_address]

            # Apply font formatting
            if 'font' in format_options:
                font_opts = format_options['font']
                cell.font = Font(
                    bold=font_opts.get('bold', False),
                    italic=font_opts.get('italic', False),
                    size=font_opts.get('size', 11),
                    color=font_opts.get('color', '000000')
                )

            # Apply alignment
            if 'alignment' in format_options:
                align_opts = format_options['alignment']
                cell.alignment = Alignment(
                    horizontal=align_opts.get('horizontal', 'general'),
                    vertical=align_opts.get('vertical', 'bottom')
                )

            # Apply fill color
            if 'fill' in format_options:
                fill_opts = format_options['fill']
                cell.fill = PatternFill(
                    start_color=fill_opts.get('color', 'FFFFFF'),
                    end_color=fill_opts.get('color', 'FFFFFF'),
                    fill_type='solid'
                )

        except Exception as e:
            print(f"Error applying formatting to {cell_address}: {str(e)}")

    def save_workbook(self):
        """
        Save the workbook to file.
        Note: Password-protected files will be saved without password protection.
        You'll need to re-apply password protection manually if needed.

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            if not self.workbook:
                print("No workbook to save")
                return False

            # Save the workbook (note: this will remove password protection)
            self.workbook.save(self.file_path)

            if self.password:
                print(f"Workbook saved to: {self.file_path}")
                print("Note: Password protection was removed. You may need to re-apply it manually.")
            else:
                print(f"Workbook saved to: {self.file_path}")

            return True

        except Exception as e:
            print(f"Error saving workbook: {str(e)}")
            return False

    def close(self):
        """
        Close the workbook.
        """
        if self.workbook:
            self.workbook.close()
            self.workbook = None
            self.worksheet = None


# Legacy function for backward compatibility
def write_data_to_excel(data, file_name='output.xlsx'):
    """
    Legacy function for writing data to Excel (backward compatibility).

    Args:
        data: Data to write (list of dictionaries)
        file_name (str): Output file name
    """
    writer = ExcelWriter(file_name)

    if writer.load_or_create_workbook():
        # Convert data to simple format and write
        if isinstance(data, dict):
            writer.write_account_balances(data)
        else:
            # Handle list of dictionaries (original format)
            for row_index, item in enumerate(data, start=1):
                if isinstance(item, dict):
                    for col_index, (key, value) in enumerate(item.items(), start=1):
                        cell_address = f"{get_column_letter(col_index)}{row_index}"
                        writer.write_to_cell(cell_address, value)

        writer.save_workbook()
        writer.close()