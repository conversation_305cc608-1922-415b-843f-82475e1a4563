from openpyxl import Workbook

def write_data_to_excel(data, file_name='output.xlsx'):
    workbook = Workbook()
    sheet = workbook.active

    # Assuming data is a list of dictionaries
    for row_index, item in enumerate(data, start=1):
        for col_index, (key, value) in enumerate(item.items(), start=1):
            sheet.cell(row=row_index, column=col_index, value=value)

    workbook.save(file_name)