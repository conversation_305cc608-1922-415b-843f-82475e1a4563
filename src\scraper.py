"""
Main Scraper Application

This is the main application that orchestrates scraping data from Monarch Money
and writing it to Excel spreadsheets.
"""

import sys
import os
import json
from datetime import datetime

# Add the src directory to the path so we can import our modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from monarch_scraper import Monarch<PERSON>oney<PERSON>craper
from excel_writer import ExcelWriter
from chrome_connector import start_chrome_with_debugging


class MonarchToExcelApp:
    """
    Main application class that coordinates scraping and Excel writing.
    """

    def __init__(self, config_file='config.json'):
        """
        Initialize the application.

        Args:
            config_file (str): Path to configuration file
        """
        self.config_file = config_file
        self.config = self.load_config()
        self.scraper = None
        self.excel_writer = None

    def load_config(self):
        """
        Load configuration from file or create default config.

        Returns:
            dict: Configuration dictionary
        """
        default_config = {
            "excel_file": "monarch_balances.xlsx",
            "sheet_name": "Account Balances",
            "chrome_debug_port": 9222,
            "cell_mapping": {
                # Example mapping - customize based on your needs
                # "Account Name": "A1",
                # "Checking": "B1",
                # "Savings": "B2",
                # "Credit Card": "B3"
            },
            "auto_save": True,
            "add_timestamp": True,
            "timestamp_cell": "A1"
        }

        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r') as f:
                    config = json.load(f)
                print(f"Loaded configuration from {self.config_file}")

                # Merge with defaults for any missing keys
                for key, value in default_config.items():
                    if key not in config:
                        config[key] = value

                return config
            else:
                print(f"Configuration file not found. Creating default: {self.config_file}")
                self.save_config(default_config)
                return default_config

        except Exception as e:
            print(f"Error loading config: {str(e)}. Using defaults.")
            return default_config

    def save_config(self, config=None):
        """
        Save configuration to file.

        Args:
            config (dict): Configuration to save (uses self.config if None)
        """
        try:
            config_to_save = config or self.config
            with open(self.config_file, 'w') as f:
                json.dump(config_to_save, f, indent=2)
            print(f"Configuration saved to {self.config_file}")
        except Exception as e:
            print(f"Error saving config: {str(e)}")

    def setup_scraper(self):
        """
        Initialize and connect the Monarch Money scraper.

        Returns:
            bool: True if setup successful, False otherwise
        """
        try:
            self.scraper = MonarchMoneyScraper(
                debug_port=self.config.get('chrome_debug_port', 9222)
            )

            if not self.scraper.connect():
                print("\nFailed to connect to Chrome.")
                print("Please make sure Chrome is running with remote debugging enabled.")
                start_chrome_with_debugging()
                return False

            if not self.scraper.verify_monarch_page():
                print("\nWarning: Current page may not be Monarch Money.")
                response = input("Continue anyway? (y/n): ").lower().strip()
                if response != 'y':
                    return False

            return True

        except Exception as e:
            print(f"Error setting up scraper: {str(e)}")
            return False

    def setup_excel_writer(self):
        """
        Initialize the Excel writer.

        Returns:
            bool: True if setup successful, False otherwise
        """
        try:
            excel_file = self.config.get('excel_file', 'monarch_balances.xlsx')
            self.excel_writer = ExcelWriter(excel_file)

            sheet_name = self.config.get('sheet_name')
            if not self.excel_writer.load_or_create_workbook(sheet_name):
                print("Failed to load/create Excel workbook")
                return False

            return True

        except Exception as e:
            print(f"Error setting up Excel writer: {str(e)}")
            return False

    def scrape_and_save(self):
        """
        Main method to scrape data and save to Excel.

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            print("Waiting for Monarch Money page to load...")
            if not self.scraper.wait_for_page_load():
                print("Page load timeout. Continuing anyway...")

            print("Extracting account balances...")
            account_data = self.scraper.extract_account_balances()

            if not account_data:
                print("No account data found. Please check that you're on the correct Monarch Money page.")
                return False

            print(f"Found {len(account_data)} accounts:")
            for account, balance in account_data.items():
                print(f"  {account}: {balance}")

            # Add timestamp if configured
            if self.config.get('add_timestamp', True):
                timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                timestamp_cell = self.config.get('timestamp_cell', 'A1')
                self.excel_writer.write_to_cell(timestamp_cell, f"Last Updated: {timestamp}")

            # Write account balances
            cell_mapping = self.config.get('cell_mapping', {})
            if not self.excel_writer.write_account_balances(account_data, cell_mapping):
                print("Failed to write account balances to Excel")
                return False

            # Save the workbook
            if self.config.get('auto_save', True):
                if not self.excel_writer.save_workbook():
                    print("Failed to save Excel workbook")
                    return False

            print(f"Successfully updated Excel file: {self.config.get('excel_file')}")
            return True

        except Exception as e:
            print(f"Error during scrape and save: {str(e)}")
            return False

    def run(self):
        """
        Run the complete application workflow.

        Returns:
            bool: True if successful, False otherwise
        """
        print("=== Monarch Money to Excel Scraper ===\n")

        try:
            # Setup scraper
            if not self.setup_scraper():
                return False

            # Setup Excel writer
            if not self.setup_excel_writer():
                return False

            # Perform scraping and saving
            success = self.scrape_and_save()

            return success

        except KeyboardInterrupt:
            print("\nOperation cancelled by user")
            return False
        except Exception as e:
            print(f"Unexpected error: {str(e)}")
            return False
        finally:
            # Cleanup
            if self.scraper:
                self.scraper.close()
            if self.excel_writer:
                self.excel_writer.close()


def main():
    """
    Main entry point for the application.
    """
    app = MonarchToExcelApp()

    if app.run():
        print("\n✅ Operation completed successfully!")
        sys.exit(0)
    else:
        print("\n❌ Operation failed. Please check the error messages above.")
        sys.exit(1)


if __name__ == "__main__":
    main()