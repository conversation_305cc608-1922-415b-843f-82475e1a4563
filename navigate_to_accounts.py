#!/usr/bin/env python3
"""
Navigation script to automatically go to Monarch Money accounts page.
"""

import sys
import os
import time

# Add the src directory to the Python path
src_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'src')
sys.path.insert(0, src_path)

try:
    from chrome_connector import ChromeConnector
except ImportError:
    from src.chrome_connector import ChromeConnector


def navigate_to_accounts():
    """Navigate to the Monarch Money accounts page."""
    
    connector = ChromeConnector()
    
    try:
        # Connect to Chrome
        if not connector.connect_to_existing_chrome():
            print("❌ Failed to connect to Chrome")
            print("Make sure Chrome is running with debugging enabled")
            return False
        
        # Check current URL
        current_url = connector.get_current_url()
        print(f"Current page: {current_url}")
        
        # Target URL for accounts page
        target_url = "https://app.monarchmoney.com/accounts?chartType=performance&dateRange=1M&timeframe=month"
        
        # If we're already on the right page, we're done
        if current_url and target_url in current_url:
            print("✅ Already on Monarch Money accounts page!")
            return True
        
        # If we're on any Monarch Money page, navigate to accounts
        if current_url and "monarchmoney.com" in current_url:
            print("📍 Navigating to accounts page...")
            connector.driver.get(target_url)
            time.sleep(3)  # Wait for page to load
            print("✅ Navigated to accounts page!")
            return True
        
        # If we're not on Monarch Money at all, go there
        print("🌐 Navigating to Monarch Money...")
        connector.driver.get("https://app.monarchmoney.com/")
        time.sleep(5)  # Wait for potential login/loading
        
        # Check if we need to login
        current_url = connector.get_current_url()
        if "login" in current_url.lower() or "auth" in current_url.lower():
            print("🔐 Please log in to Monarch Money in the browser window")
            print("⏳ Waiting 30 seconds for you to log in...")
            time.sleep(30)
        
        # Now navigate to accounts page
        print("📍 Navigating to accounts page...")
        connector.driver.get(target_url)
        time.sleep(3)
        
        # Verify we're on the right page
        final_url = connector.get_current_url()
        if final_url and "accounts" in final_url:
            print("✅ Successfully navigated to accounts page!")
            return True
        else:
            print("⚠️  Navigation may not have completed successfully")
            print(f"Final URL: {final_url}")
            return False
            
    except Exception as e:
        print(f"❌ Error during navigation: {str(e)}")
        return False
    
    finally:
        # Don't close the connection - leave it for the scraper
        pass


if __name__ == "__main__":
    success = navigate_to_accounts()
    if success:
        print("🎯 Ready for balance extraction!")
        sys.exit(0)
    else:
        print("❌ Navigation failed")
        sys.exit(1)
