"""
Monarch Money Scraper Module

This module contains specific logic for scraping account balance data
from Monarch Money web pages.
"""

import time
import re
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
try:
    from .chrome_connector import ChromeConnector
except ImportError:
    from chrome_connector import ChromeConnector


class MonarchMoneyScraper:
    """
    A specialized scraper for extracting account balance data from Monarch Money.
    """
    
    def __init__(self, debug_port=9222):
        """
        Initialize the Monarch Money scraper.
        
        Args:
            debug_port (int): Chrome debug port (default: 9222)
        """
        self.connector = ChromeConnector(debug_port)
        self.account_data = {}
        
    def connect(self):
        """
        Connect to the existing Chrome session.
        
        Returns:
            bool: True if connection successful, False otherwise
        """
        return self.connector.connect_to_existing_chrome()
    
    def verify_monarch_page(self):
        """
        Verify that we're on a Monarch Money page and identify page type.

        Returns:
            bool: True if on Monarch Money page, False otherwise
        """
        current_url = self.connector.get_current_url()
        if not current_url:
            return False

        # Check if URL contains monarch money domain
        monarch_domains = ['monarchmoney.com', 'monarch.money']
        is_monarch_page = any(domain in current_url.lower() for domain in monarch_domains)

        if is_monarch_page:
            print(f"Confirmed Monarch Money page: {current_url}")

            # Identify page type for optimization
            if '/accounts' in current_url:
                print("Detected: Accounts page - using optimized selectors")
            elif '/dashboard' in current_url:
                print("Detected: Dashboard page")
            elif '/net-worth' in current_url:
                print("Detected: Net Worth page")
        else:
            print(f"Warning: Current page may not be Monarch Money: {current_url}")

        return is_monarch_page
    
    def wait_for_page_load(self, timeout=5):
        """
        Wait for the Monarch Money page to fully load.

        Args:
            timeout (int): Maximum time to wait in seconds

        Returns:
            bool: True if page loaded successfully
        """
        try:
            # Quick check for page readiness - reduced timeout for speed
            main_content = self.connector.wait_for_element(
                "main, [data-testid='main-content'], .main-content",
                timeout=timeout
            )

            if main_content:
                time.sleep(1)  # Reduced wait time
                return True
            else:
                # Don't fail - just continue, the page might be ready anyway
                return True

        except Exception as e:
            # Don't fail on timeout - continue anyway for speed
            return True
    
    def extract_account_balances(self, target_accounts=None):
        """
        Extract account balance information from the current Monarch Money page.

        Args:
            target_accounts (list): List of specific account names to look for (for speed)

        Returns:
            dict: Dictionary containing account names and their balances
        """
        balances = {}

        try:
            # URL-specific optimization for accounts page
            current_url = self.connector.get_current_url()
            account_elements = []

            if current_url and "accounts" in current_url:
                # Optimized selectors for the accounts page
                accounts_page_selectors = [
                    # Common patterns for account rows/items on accounts page
                    '[data-testid*="account-row"]',
                    '[data-testid*="account-item"]',
                    '.account-row',
                    '.account-item',
                    '[class*="AccountRow"]',
                    '[class*="account-row"]',
                    'tr[data-testid]',  # Table rows with test IDs
                    '[role="row"]',     # ARIA table rows
                    # Investment account specific
                    '[data-testid*="investment"]',
                    '[class*="investment"]'
                ]

                # Try accounts page specific selectors first
                for selector in accounts_page_selectors:
                    elements = self.connector.find_elements(selector)
                    if elements and len(elements) < 200:  # Reasonable number
                        account_elements = elements
                        print(f"Using accounts page selector: {selector} ({len(elements)} elements)")
                        break

            # Fallback: currency-based search but more comprehensive
            if not account_elements:
                print("Using fallback currency search...")
                all_elements = self.connector.find_elements("*")

                for elem in all_elements:
                    elem_text = self.connector.get_element_text(elem)
                    if self._contains_currency(elem_text):
                        account_elements.append(elem)
                        # Process enough elements to find all accounts (was too restrictive before)
                        if len(account_elements) >= 300:  # Increased to ensure we get all accounts
                            break

                print(f"Found {len(account_elements)} elements with currency symbols")
            
            # Extract balance information - optimized for speed
            target_found = 0
            target_count = len(target_accounts) if target_accounts else 7

            for i, element in enumerate(account_elements):
                try:
                    element_text = self.connector.get_element_text(element)

                    # Skip empty elements
                    if not element_text.strip():
                        continue

                    # Look for currency amounts in the text
                    amounts = self._extract_currency_amounts(element_text)

                    if amounts:
                        # Try to find account name (look in parent or sibling elements)
                        account_name = self._find_account_name(element)

                        if not account_name:
                            account_name = f"Account_{i+1}"

                        # Speed optimization: if we have target accounts, only process those
                        if target_accounts and account_name not in target_accounts:
                            continue

                        # Convert amount to float for comparison
                        amount_value = self._clean_currency_value(amounts[0])

                        # Handle duplicate account names - keep the larger balance
                        if account_name in balances:
                            existing_value = self._clean_currency_value(balances[account_name])
                            if isinstance(amount_value, (int, float)) and isinstance(existing_value, (int, float)):
                                if amount_value > existing_value:
                                    balances[account_name] = amounts[0]
                            # Reduced logging for speed
                        else:
                            # Store the balance (use the first amount found)
                            balances[account_name] = amounts[0]
                            if target_accounts and account_name in target_accounts:
                                target_found += 1
                                print(f"Found target: {account_name} = {amounts[0]}")

                        # Speed optimization: stop early if we found all target accounts
                        if target_accounts and target_found >= target_count:
                            print(f"Found all {target_count} target accounts, stopping search")
                            break

                except Exception as e:
                    # Reduced error logging for speed
                    continue

            # If we didn't find enough target accounts, try the more comprehensive fallback
            found_targets = [acc for acc in target_accounts if acc in balances] if target_accounts else []
            if target_accounts and len(found_targets) < len(target_accounts):
                missing = [acc for acc in target_accounts if acc not in balances]
                print(f"Missing {len(missing)} accounts: {missing}")
                print("Trying comprehensive fallback method...")
                fallback_balances = self._fallback_balance_extraction()
                # Add any missing target accounts from fallback
                for acc in missing:
                    if acc in fallback_balances:
                        balances[acc] = fallback_balances[acc]
                        print(f"Found via fallback: {acc} = {fallback_balances[acc]}")

            self.account_data = balances
            return balances
            
        except Exception as e:
            print(f"Error extracting account balances: {str(e)}")
            return {}
    
    def _contains_currency(self, text):
        """
        Check if text contains currency symbols or patterns.
        
        Args:
            text (str): Text to check
            
        Returns:
            bool: True if text contains currency patterns
        """
        currency_patterns = [
            r'\$[\d,]+\.?\d*',  # Dollar amounts
            r'[\d,]+\.?\d*\s*USD',  # USD amounts
            r'-?\$[\d,]+\.?\d*',  # Negative dollar amounts
        ]
        
        for pattern in currency_patterns:
            if re.search(pattern, text):
                return True
        return False
    
    def _extract_currency_amounts(self, text):
        """
        Extract currency amounts from text.
        
        Args:
            text (str): Text to extract amounts from
            
        Returns:
            list: List of found currency amounts
        """
        # Patterns to match various currency formats
        patterns = [
            r'-?\$[\d,]+\.?\d*',  # $1,234.56 or -$1,234.56
            r'-?[\d,]+\.?\d*\s*USD',  # 1,234.56 USD
            r'-?[\d,]+\.?\d*',  # 1,234.56 (if in context of money)
        ]
        
        amounts = []
        for pattern in patterns:
            matches = re.findall(pattern, text)
            amounts.extend(matches)
        
        return amounts
    
    def _find_account_name(self, element):
        """
        Try to find the account name associated with a balance element.

        Args:
            element: WebElement containing balance information

        Returns:
            str: Account name or None if not found
        """
        try:
            # Try to find account name in parent elements
            parent = element.find_element(By.XPATH, "..")
            parent_text = self.connector.get_element_text(parent)

            # Look for text that doesn't contain currency symbols
            lines = parent_text.split('\n')
            for line in lines:
                line = line.strip()
                if line and not self._contains_currency(line):
                    # This might be the account name
                    return line

            # Try sibling elements
            siblings = parent.find_elements(By.XPATH, "./*")
            for sibling in siblings:
                sibling_text = self.connector.get_element_text(sibling)
                if sibling_text and not self._contains_currency(sibling_text):
                    return sibling_text.strip()

        except Exception as e:
            # Reduced logging for speed
            pass

        return None
    
    def _fallback_balance_extraction(self):
        """
        Comprehensive fallback method to extract balances when standard methods fail.

        Returns:
            dict: Dictionary of found balances
        """
        balances = {}

        try:
            # Get all text content from the page - this is comprehensive but fast
            page_text = self.connector.execute_script("return document.body.innerText;")

            if page_text:
                # Split into lines and look for currency amounts
                lines = page_text.split('\n')

                for i, line in enumerate(lines):
                    line = line.strip()
                    if self._contains_currency(line):
                        amounts = self._extract_currency_amounts(line)
                        if amounts:
                            # Use the line itself or nearby lines as account name
                            account_name = f"Balance_{len(balances)+1}"

                            # Look for a descriptive name in nearby lines (expanded search)
                            for j in range(max(0, i-5), min(len(lines), i+5)):  # Increased range
                                nearby_line = lines[j].strip()
                                if (nearby_line and
                                    not self._contains_currency(nearby_line) and
                                    len(nearby_line) > 3 and
                                    len(nearby_line) < 100):  # Increased max length
                                    # Prefer lines that contain known account keywords
                                    if any(keyword in nearby_line.lower() for keyword in
                                          ['401k', 'ira', 'roth', 'investment', 'health', 'savings', 'employed']):
                                        account_name = nearby_line
                                        break
                                    elif account_name.startswith('Balance_'):  # Only update if we haven't found a good name yet
                                        account_name = nearby_line

                            # Handle duplicates by keeping larger amounts
                            if account_name in balances:
                                existing_value = self._clean_currency_value(balances[account_name])
                                new_value = self._clean_currency_value(amounts[0])
                                if isinstance(new_value, (int, float)) and isinstance(existing_value, (int, float)):
                                    if new_value > existing_value:
                                        balances[account_name] = amounts[0]
                            else:
                                balances[account_name] = amounts[0]

        except Exception as e:
            print(f"Error in fallback extraction: {str(e)}")

        return balances
    
    def get_account_data(self):
        """
        Get the extracted account data.

        Returns:
            dict: Dictionary containing account names and balances
        """
        return self.account_data

    def _clean_currency_value(self, value):
        """
        Clean currency value for comparison (remove symbols, convert to number).

        Args:
            value (str): Currency value string

        Returns:
            float or str: Cleaned numeric value or original string if conversion fails
        """
        try:
            if isinstance(value, (int, float)):
                return value

            # Remove common currency symbols and formatting
            clean_value = str(value).replace('$', '').replace(',', '').replace(' USD', '').strip()

            # Try to convert to float
            return float(clean_value)

        except (ValueError, TypeError):
            # If conversion fails, return original value
            return value

    def close(self):
        """
        Close the Chrome connection.
        """
        self.connector.close_connection()
