"""
Monarch Money Scraper Module

This module contains specific logic for scraping account balance data
from Monarch Money web pages.
"""

import time
import re
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
try:
    from .chrome_connector import ChromeConnector
except ImportError:
    from chrome_connector import ChromeConnector


class MonarchMoneyScraper:
    """
    A specialized scraper for extracting account balance data from Monarch Money.
    """
    
    def __init__(self, debug_port=9222):
        """
        Initialize the Monarch Money scraper.
        
        Args:
            debug_port (int): Chrome debug port (default: 9222)
        """
        self.connector = ChromeConnector(debug_port)
        self.account_data = {}
        
    def connect(self):
        """
        Connect to the existing Chrome session.
        
        Returns:
            bool: True if connection successful, False otherwise
        """
        return self.connector.connect_to_existing_chrome()
    
    def verify_monarch_page(self):
        """
        Verify that we're on a Monarch Money page.
        
        Returns:
            bool: True if on Monarch Money page, False otherwise
        """
        current_url = self.connector.get_current_url()
        if not current_url:
            return False
        
        # Check if URL contains monarch money domain
        monarch_domains = ['monarchmoney.com', 'monarch.money']
        is_monarch_page = any(domain in current_url.lower() for domain in monarch_domains)
        
        if is_monarch_page:
            print(f"Confirmed Monarch Money page: {current_url}")
        else:
            print(f"Warning: Current page may not be Monarch Money: {current_url}")
        
        return is_monarch_page
    
    def wait_for_page_load(self, timeout=5):
        """
        Wait for the Monarch Money page to fully load.

        Args:
            timeout (int): Maximum time to wait in seconds

        Returns:
            bool: True if page loaded successfully
        """
        try:
            # Quick check for page readiness - reduced timeout for speed
            main_content = self.connector.wait_for_element(
                "main, [data-testid='main-content'], .main-content",
                timeout=timeout
            )

            if main_content:
                time.sleep(1)  # Reduced wait time
                return True
            else:
                # Don't fail - just continue, the page might be ready anyway
                return True

        except Exception as e:
            # Don't fail on timeout - continue anyway for speed
            return True
    
    def extract_account_balances(self, target_accounts=None):
        """
        Extract account balance information from the current Monarch Money page.

        Args:
            target_accounts (list): List of specific account names to look for (for speed)

        Returns:
            dict: Dictionary containing account names and their balances
        """
        balances = {}

        try:
            # Fast approach: look for specific investment account patterns first
            investment_selectors = [
                # Target investment-specific selectors for speed
                '[data-testid*="investment"]',
                '[data-testid*="account"]',
                '.investment-account',
                '[class*="investment"]',
                '[class*="retirement"]',
                '[class*="401k"]',
                '[class*="ira"]'
            ]

            # Try investment-specific selectors first
            account_elements = []
            for selector in investment_selectors:
                elements = self.connector.find_elements(selector)
                if elements and len(elements) < 100:  # Avoid massive element lists
                    account_elements = elements
                    break

            # If no specific elements found, use broader search but limit scope
            if not account_elements:
                # More targeted fallback - look for elements with currency symbols
                # but limit to reasonable number for speed
                all_elements = self.connector.find_elements("*")
                account_elements = []

                for elem in all_elements[:200]:  # Limit to first 200 elements for speed
                    elem_text = self.connector.get_element_text(elem)
                    if self._contains_currency(elem_text):
                        account_elements.append(elem)
                        if len(account_elements) >= 50:  # Stop after finding 50 currency elements
                            break
            
            # Extract balance information - optimized for speed
            target_found = 0
            target_count = len(target_accounts) if target_accounts else 7

            for i, element in enumerate(account_elements):
                try:
                    element_text = self.connector.get_element_text(element)

                    # Skip empty elements
                    if not element_text.strip():
                        continue

                    # Look for currency amounts in the text
                    amounts = self._extract_currency_amounts(element_text)

                    if amounts:
                        # Try to find account name (look in parent or sibling elements)
                        account_name = self._find_account_name(element)

                        if not account_name:
                            account_name = f"Account_{i+1}"

                        # Speed optimization: if we have target accounts, only process those
                        if target_accounts and account_name not in target_accounts:
                            continue

                        # Convert amount to float for comparison
                        amount_value = self._clean_currency_value(amounts[0])

                        # Handle duplicate account names - keep the larger balance
                        if account_name in balances:
                            existing_value = self._clean_currency_value(balances[account_name])
                            if isinstance(amount_value, (int, float)) and isinstance(existing_value, (int, float)):
                                if amount_value > existing_value:
                                    balances[account_name] = amounts[0]
                            # Reduced logging for speed
                        else:
                            # Store the balance (use the first amount found)
                            balances[account_name] = amounts[0]
                            if target_accounts and account_name in target_accounts:
                                target_found += 1
                                print(f"Found target: {account_name} = {amounts[0]}")

                        # Speed optimization: stop early if we found all target accounts
                        if target_accounts and target_found >= target_count:
                            print(f"Found all {target_count} target accounts, stopping search")
                            break

                except Exception as e:
                    # Reduced error logging for speed
                    continue

            # Only use fallback if we didn't find any target accounts
            if target_accounts and not any(acc in balances for acc in target_accounts):
                print("Target accounts not found, trying fallback method...")
                fallback_balances = self._fallback_balance_extraction()
                # Only keep target accounts from fallback
                for acc in target_accounts:
                    if acc in fallback_balances:
                        balances[acc] = fallback_balances[acc]

            self.account_data = balances
            return balances
            
        except Exception as e:
            print(f"Error extracting account balances: {str(e)}")
            return {}
    
    def _contains_currency(self, text):
        """
        Check if text contains currency symbols or patterns.
        
        Args:
            text (str): Text to check
            
        Returns:
            bool: True if text contains currency patterns
        """
        currency_patterns = [
            r'\$[\d,]+\.?\d*',  # Dollar amounts
            r'[\d,]+\.?\d*\s*USD',  # USD amounts
            r'-?\$[\d,]+\.?\d*',  # Negative dollar amounts
        ]
        
        for pattern in currency_patterns:
            if re.search(pattern, text):
                return True
        return False
    
    def _extract_currency_amounts(self, text):
        """
        Extract currency amounts from text.
        
        Args:
            text (str): Text to extract amounts from
            
        Returns:
            list: List of found currency amounts
        """
        # Patterns to match various currency formats
        patterns = [
            r'-?\$[\d,]+\.?\d*',  # $1,234.56 or -$1,234.56
            r'-?[\d,]+\.?\d*\s*USD',  # 1,234.56 USD
            r'-?[\d,]+\.?\d*',  # 1,234.56 (if in context of money)
        ]
        
        amounts = []
        for pattern in patterns:
            matches = re.findall(pattern, text)
            amounts.extend(matches)
        
        return amounts
    
    def _find_account_name(self, element):
        """
        Try to find the account name associated with a balance element.

        Args:
            element: WebElement containing balance information

        Returns:
            str: Account name or None if not found
        """
        try:
            # Try to find account name in parent elements
            parent = element.find_element(By.XPATH, "..")
            parent_text = self.connector.get_element_text(parent)

            # Look for text that doesn't contain currency symbols
            lines = parent_text.split('\n')
            for line in lines:
                line = line.strip()
                if line and not self._contains_currency(line):
                    # This might be the account name
                    return line

            # Try sibling elements
            siblings = parent.find_elements(By.XPATH, "./*")
            for sibling in siblings:
                sibling_text = self.connector.get_element_text(sibling)
                if sibling_text and not self._contains_currency(sibling_text):
                    return sibling_text.strip()

        except Exception as e:
            # Reduced logging for speed
            pass

        return None
    
    def _fallback_balance_extraction(self):
        """
        Fallback method to extract balances when standard methods fail.
        
        Returns:
            dict: Dictionary of found balances
        """
        balances = {}
        
        try:
            # Get all text content from the page
            page_text = self.connector.execute_script("return document.body.innerText;")
            
            if page_text:
                # Split into lines and look for currency amounts
                lines = page_text.split('\n')
                
                for i, line in enumerate(lines):
                    line = line.strip()
                    if self._contains_currency(line):
                        amounts = self._extract_currency_amounts(line)
                        if amounts:
                            # Use the line itself or nearby lines as account name
                            account_name = f"Balance_{len(balances)+1}"
                            
                            # Look for a descriptive name in nearby lines
                            for j in range(max(0, i-3), min(len(lines), i+3)):
                                nearby_line = lines[j].strip()
                                if (nearby_line and 
                                    not self._contains_currency(nearby_line) and 
                                    len(nearby_line) > 3 and 
                                    len(nearby_line) < 50):
                                    account_name = nearby_line
                                    break
                            
                            balances[account_name] = amounts[0]
            
        except Exception as e:
            print(f"Error in fallback extraction: {str(e)}")
        
        return balances
    
    def get_account_data(self):
        """
        Get the extracted account data.

        Returns:
            dict: Dictionary containing account names and balances
        """
        return self.account_data

    def _clean_currency_value(self, value):
        """
        Clean currency value for comparison (remove symbols, convert to number).

        Args:
            value (str): Currency value string

        Returns:
            float or str: Cleaned numeric value or original string if conversion fails
        """
        try:
            if isinstance(value, (int, float)):
                return value

            # Remove common currency symbols and formatting
            clean_value = str(value).replace('$', '').replace(',', '').replace(' USD', '').strip()

            # Try to convert to float
            return float(clean_value)

        except (ValueError, TypeError):
            # If conversion fails, return original value
            return value

    def close(self):
        """
        Close the Chrome connection.
        """
        self.connector.close_connection()
