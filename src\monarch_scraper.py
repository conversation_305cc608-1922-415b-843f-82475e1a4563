"""
Monarch Money Scraper Module

This module contains specific logic for scraping account balance data
from Monarch Money web pages.
"""

import time
import re
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
try:
    from .chrome_connector import ChromeConnector
except ImportError:
    from chrome_connector import ChromeConnector


class MonarchMoneyScraper:
    """
    A specialized scraper for extracting account balance data from Monarch Money.
    """
    
    def __init__(self, debug_port=9222):
        """
        Initialize the Monarch Money scraper.
        
        Args:
            debug_port (int): Chrome debug port (default: 9222)
        """
        self.connector = ChromeConnector(debug_port)
        self.account_data = {}
        
    def connect(self):
        """
        Connect to the existing Chrome session.
        
        Returns:
            bool: True if connection successful, False otherwise
        """
        return self.connector.connect_to_existing_chrome()
    
    def verify_monarch_page(self):
        """
        Verify that we're on a Monarch Money page.
        
        Returns:
            bool: True if on Monarch Money page, False otherwise
        """
        current_url = self.connector.get_current_url()
        if not current_url:
            return False
        
        # Check if URL contains monarch money domain
        monarch_domains = ['monarchmoney.com', 'monarch.money']
        is_monarch_page = any(domain in current_url.lower() for domain in monarch_domains)
        
        if is_monarch_page:
            print(f"Confirmed Monarch Money page: {current_url}")
        else:
            print(f"Warning: Current page may not be Monarch Money: {current_url}")
        
        return is_monarch_page
    
    def wait_for_page_load(self, timeout=15):
        """
        Wait for the Monarch Money page to fully load.
        
        Args:
            timeout (int): Maximum time to wait in seconds
            
        Returns:
            bool: True if page loaded successfully
        """
        try:
            # Wait for common Monarch Money elements to load
            # This may need adjustment based on the actual page structure
            
            # Wait for main content area
            main_content = self.connector.wait_for_element(
                "main, [data-testid='main-content'], .main-content", 
                timeout=timeout
            )
            
            if main_content:
                print("Main content loaded")
                time.sleep(2)  # Additional wait for dynamic content
                return True
            else:
                print("Main content not found - page may not have loaded properly")
                return False
                
        except Exception as e:
            print(f"Error waiting for page load: {str(e)}")
            return False
    
    def extract_account_balances(self):
        """
        Extract account balance information from the current Monarch Money page.
        
        Returns:
            dict: Dictionary containing account names and their balances
        """
        balances = {}
        
        try:
            # Common selectors that might contain account information
            # These may need to be updated based on Monarch Money's actual HTML structure
            
            account_selectors = [
                # Try different possible selectors for account containers
                '[data-testid*="account"]',
                '.account-item',
                '.account-row',
                '[class*="account"]',
                '.balance-item',
                '[data-testid*="balance"]'
            ]
            
            balance_selectors = [
                '[data-testid*="balance"]',
                '.balance',
                '.amount',
                '[class*="balance"]',
                '[class*="amount"]'
            ]
            
            # Try to find account elements
            account_elements = []
            for selector in account_selectors:
                elements = self.connector.find_elements(selector)
                if elements:
                    account_elements = elements
                    print(f"Found {len(elements)} account elements using selector: {selector}")
                    break
            
            if not account_elements:
                # Fallback: try to find any elements with currency symbols
                currency_elements = self.connector.find_elements("*")
                account_elements = [elem for elem in currency_elements 
                                 if self._contains_currency(self.connector.get_element_text(elem))]
                print(f"Fallback: Found {len(account_elements)} elements with currency symbols")
            
            # Extract balance information
            for i, element in enumerate(account_elements):
                try:
                    element_text = self.connector.get_element_text(element)
                    
                    # Skip empty elements
                    if not element_text.strip():
                        continue
                    
                    # Look for currency amounts in the text
                    amounts = self._extract_currency_amounts(element_text)
                    
                    if amounts:
                        # Try to find account name (look in parent or sibling elements)
                        account_name = self._find_account_name(element)
                        
                        if not account_name:
                            account_name = f"Account_{i+1}"
                        
                        # Store the balance (use the first amount found)
                        balances[account_name] = amounts[0]
                        print(f"Found: {account_name} = {amounts[0]}")
                
                except Exception as e:
                    print(f"Error processing account element {i}: {str(e)}")
                    continue
            
            # If no balances found, try a more aggressive approach
            if not balances:
                balances = self._fallback_balance_extraction()
            
            self.account_data = balances
            return balances
            
        except Exception as e:
            print(f"Error extracting account balances: {str(e)}")
            return {}
    
    def _contains_currency(self, text):
        """
        Check if text contains currency symbols or patterns.
        
        Args:
            text (str): Text to check
            
        Returns:
            bool: True if text contains currency patterns
        """
        currency_patterns = [
            r'\$[\d,]+\.?\d*',  # Dollar amounts
            r'[\d,]+\.?\d*\s*USD',  # USD amounts
            r'-?\$[\d,]+\.?\d*',  # Negative dollar amounts
        ]
        
        for pattern in currency_patterns:
            if re.search(pattern, text):
                return True
        return False
    
    def _extract_currency_amounts(self, text):
        """
        Extract currency amounts from text.
        
        Args:
            text (str): Text to extract amounts from
            
        Returns:
            list: List of found currency amounts
        """
        # Patterns to match various currency formats
        patterns = [
            r'-?\$[\d,]+\.?\d*',  # $1,234.56 or -$1,234.56
            r'-?[\d,]+\.?\d*\s*USD',  # 1,234.56 USD
            r'-?[\d,]+\.?\d*',  # 1,234.56 (if in context of money)
        ]
        
        amounts = []
        for pattern in patterns:
            matches = re.findall(pattern, text)
            amounts.extend(matches)
        
        return amounts
    
    def _find_account_name(self, element):
        """
        Try to find the account name associated with a balance element.
        
        Args:
            element: WebElement containing balance information
            
        Returns:
            str: Account name or None if not found
        """
        try:
            # Try to find account name in parent elements
            parent = element.find_element(By.XPATH, "..")
            parent_text = self.connector.get_element_text(parent)
            
            # Look for text that doesn't contain currency symbols
            lines = parent_text.split('\n')
            for line in lines:
                line = line.strip()
                if line and not self._contains_currency(line):
                    # This might be the account name
                    return line
            
            # Try sibling elements
            siblings = parent.find_elements(By.XPATH, "./*")
            for sibling in siblings:
                sibling_text = self.connector.get_element_text(sibling)
                if sibling_text and not self._contains_currency(sibling_text):
                    return sibling_text.strip()
            
        except Exception as e:
            print(f"Error finding account name: {str(e)}")
        
        return None
    
    def _fallback_balance_extraction(self):
        """
        Fallback method to extract balances when standard methods fail.
        
        Returns:
            dict: Dictionary of found balances
        """
        balances = {}
        
        try:
            # Get all text content from the page
            page_text = self.connector.execute_script("return document.body.innerText;")
            
            if page_text:
                # Split into lines and look for currency amounts
                lines = page_text.split('\n')
                
                for i, line in enumerate(lines):
                    line = line.strip()
                    if self._contains_currency(line):
                        amounts = self._extract_currency_amounts(line)
                        if amounts:
                            # Use the line itself or nearby lines as account name
                            account_name = f"Balance_{len(balances)+1}"
                            
                            # Look for a descriptive name in nearby lines
                            for j in range(max(0, i-3), min(len(lines), i+3)):
                                nearby_line = lines[j].strip()
                                if (nearby_line and 
                                    not self._contains_currency(nearby_line) and 
                                    len(nearby_line) > 3 and 
                                    len(nearby_line) < 50):
                                    account_name = nearby_line
                                    break
                            
                            balances[account_name] = amounts[0]
            
        except Exception as e:
            print(f"Error in fallback extraction: {str(e)}")
        
        return balances
    
    def get_account_data(self):
        """
        Get the extracted account data.
        
        Returns:
            dict: Dictionary containing account names and balances
        """
        return self.account_data
    
    def close(self):
        """
        Close the Chrome connection.
        """
        self.connector.close_connection()
