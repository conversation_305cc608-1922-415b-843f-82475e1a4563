"""
Test Script for Monarch Money Scraper

This script provides basic testing functionality for the scraper components.
"""

import sys
import os
import unittest
from unittest.mock import Mock, patch

# Add src directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.excel_writer import ExcelWriter
from src.chrome_connector import ChromeConnector


class TestExcelWriter(unittest.TestCase):
    """Test cases for ExcelWriter class."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.test_file = 'test_output.xlsx'
        self.writer = ExcelWriter(self.test_file)
    
    def tearDown(self):
        """Clean up test files."""
        if os.path.exists(self.test_file):
            os.remove(self.test_file)
    
    def test_create_workbook(self):
        """Test workbook creation."""
        result = self.writer.load_or_create_workbook()
        self.assertTrue(result)
        self.assertIsNotNone(self.writer.workbook)
        self.assertIsNotNone(self.writer.worksheet)
    
    def test_write_to_cell(self):
        """Test writing to specific cells."""
        self.writer.load_or_create_workbook()
        
        result = self.writer.write_to_cell('A1', 'Test Value')
        self.assertTrue(result)
        
        # Verify the value was written
        self.assertEqual(self.writer.worksheet['A1'].value, 'Test Value')
    
    def test_write_account_balances(self):
        """Test writing account balance data."""
        self.writer.load_or_create_workbook()
        
        test_data = {
            'Checking': '$1,234.56',
            'Savings': '$5,678.90'
        }
        
        result = self.writer.write_account_balances(test_data)
        self.assertTrue(result)
    
    def test_clean_currency_value(self):
        """Test currency value cleaning."""
        test_cases = [
            ('$1,234.56', 1234.56),
            ('-$500.00', -500.00),
            ('1,000 USD', 1000.0),
            ('invalid', 'invalid')
        ]
        
        for input_val, expected in test_cases:
            result = self.writer._clean_currency_value(input_val)
            self.assertEqual(result, expected)


class TestChromeConnector(unittest.TestCase):
    """Test cases for ChromeConnector class."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.connector = ChromeConnector()
    
    def test_initialization(self):
        """Test connector initialization."""
        self.assertEqual(self.connector.debug_port, 9222)
        self.assertIsNone(self.connector.driver)
    
    def test_currency_detection(self):
        """Test currency pattern detection (if we had access to the scraper)."""
        # This would require importing the scraper, but we can test the concept
        test_texts = [
            '$1,234.56',
            '1,234.56 USD',
            '-$500.00',
            'No currency here'
        ]
        
        # We would test the _contains_currency method here
        # For now, just verify the test structure works
        self.assertTrue(True)


def run_integration_test():
    """
    Run a basic integration test to verify the components work together.
    This test doesn't require Chrome to be running.
    """
    print("Running integration test...")
    
    try:
        # Test Excel writer
        print("Testing Excel writer...")
        writer = ExcelWriter('integration_test.xlsx')
        
        if writer.load_or_create_workbook('Test Sheet'):
            print("✅ Excel workbook creation: PASSED")
        else:
            print("❌ Excel workbook creation: FAILED")
            return False
        
        # Test writing data
        test_data = {
            'Test Account 1': '$1,000.00',
            'Test Account 2': '$2,500.50'
        }
        
        if writer.write_account_balances(test_data):
            print("✅ Account balance writing: PASSED")
        else:
            print("❌ Account balance writing: FAILED")
            return False
        
        if writer.save_workbook():
            print("✅ Workbook saving: PASSED")
        else:
            print("❌ Workbook saving: FAILED")
            return False
        
        writer.close()
        
        # Clean up
        if os.path.exists('integration_test.xlsx'):
            os.remove('integration_test.xlsx')
        
        print("✅ Integration test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Integration test failed: {str(e)}")
        return False


def test_chrome_connection():
    """
    Test Chrome connection (requires Chrome to be running with debug port).
    """
    print("Testing Chrome connection...")
    
    try:
        connector = ChromeConnector()
        
        if connector.connect_to_existing_chrome():
            print("✅ Chrome connection: PASSED")
            
            # Test getting current URL
            url = connector.get_current_url()
            if url:
                print(f"✅ Current URL retrieval: PASSED ({url})")
            else:
                print("⚠️  Current URL retrieval: No URL found")
            
            connector.close_connection()
            return True
        else:
            print("❌ Chrome connection: FAILED")
            print("Make sure Chrome is running with --remote-debugging-port=9222")
            return False
            
    except Exception as e:
        print(f"❌ Chrome connection test failed: {str(e)}")
        return False


def main():
    """Main test runner."""
    print("=== Monarch Money Scraper Test Suite ===\n")
    
    # Run unit tests
    print("Running unit tests...")
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    print("\n" + "="*50 + "\n")
    
    # Run integration test
    integration_success = run_integration_test()
    
    print("\n" + "="*50 + "\n")
    
    # Test Chrome connection (optional)
    response = input("Test Chrome connection? (requires Chrome with debug port) (y/n): ").lower().strip()
    if response == 'y':
        chrome_success = test_chrome_connection()
    else:
        chrome_success = True
        print("Chrome connection test skipped")
    
    print("\n" + "="*50 + "\n")
    
    # Summary
    if integration_success and chrome_success:
        print("✅ All tests completed successfully!")
        return True
    else:
        print("❌ Some tests failed. Please check the output above.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
