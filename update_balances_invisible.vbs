Set objShell = CreateObject("WScript.Shell")

' Change to the script directory and run the update
objShell.CurrentDirectory = "C:\Users\<USER>\web-scraper-to-excel"

' Run navigation script invisibly
objShell.Run "python navigate_to_accounts.py", 0, True

' Run scraper invisibly  
objShell.Run "python run_scraper.py", 0, True

' Optional: Show a brief notification when complete
objShell.Popup "Monarch Money balances updated!", 2, "Update Complete", 64
