Set objShell = CreateObject("WScript.Shell")
Set objFSO = CreateObject("Scripting.FileSystemObject")

' Change to the script directory and run the update
objShell.CurrentDirectory = "C:\Users\<USER>\web-scraper-to-excel"

' Delete any existing completion flag
If objFSO.FileExists("update_complete.flag") Then
    objFSO.DeleteFile "update_complete.flag"
End If

' Run navigation script invisibly (this ensures we're on the right page)
objShell.Run "python navigate_to_accounts.py", 0, True

' Wait a moment for navigation to complete
WScript.Sleep 3000

' Run scraper invisibly
objShell.Run "python run_scraper.py", 0, True

' Create completion flag file
Set flagFile = objFSO.CreateTextFile("update_complete.flag", True)
flagFile.WriteLine "Update completed at " & Now
flagFile.Close
