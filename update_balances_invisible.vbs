Set objShell = CreateObject("WScript.Shell")
Set objFSO = CreateObject("Scripting.FileSystemObject")

' Change to the script directory and run the update
objShell.CurrentDirectory = "C:\Users\<USER>\web-scraper-to-excel"

' Delete any existing completion flag
If objFSO.FileExists("update_complete.flag") Then
    objFSO.DeleteFile "update_complete.flag"
End If

' Run navigation script invisibly
objShell.Run "python navigate_to_accounts.py", 0, True

' Run scraper invisibly
objShell.Run "python run_scraper.py", 0, True

' Create completion flag file
Set flagFile = objFSO.CreateTextFile("update_complete.flag", True)
flagFile.WriteLine "Update completed at " & Now
flagFile.Close

' Optional: Show a brief notification when complete (commented out for silence)
' objShell.Popup "Monarch Money balances updated!", 2, "Update Complete", 64
